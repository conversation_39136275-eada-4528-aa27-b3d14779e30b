# WordPress Docker Setup

This Docker setup provides a complete WordPress development environment with MySQL database and phpMyAdmin.

## What's Included

- **WordPress**: Latest WordPress with Apache and PHP
- **MySQL 8.0**: Database server
- **phpMyAdmin**: Web-based MySQL administration tool

## Quick Start

### Prerequisites
- Docker installed on your system
- Docker Compose installed

### 1. Build and Start the Containers

```bash
# Build and start all services
docker-compose up -d --build

# Or just start (if already built)
docker-compose up -d
```

### 2. Access Your WordPress Site

- **WordPress Site**: http://localhost:8080
- **phpMyAdmin**: http://localhost:8081

### 3. WordPress Installation

1. Open http://localhost:8080 in your browser
2. Follow the WordPress installation wizard
3. Use these database settings:
   - **Database Name**: wordpress
   - **Username**: wordpress
   - **Password**: wordpress_password
   - **Database Host**: db
   - **Table Prefix**: wp_

## Useful Commands

### View Running Containers
```bash
docker-compose ps
```

### View Logs
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs wordpress
docker-compose logs db
```

### Stop Services
```bash
docker-compose down
```

### Stop and Remove Volumes (⚠️ This will delete your data)
```bash
docker-compose down -v
```

### Access Container Shell
```bash
# WordPress container
docker-compose exec wordpress bash

# Database container
docker-compose exec db bash
```

### Backup Database
```bash
docker-compose exec db mysqldump -u wordpress -pwordpress_password wordpress > backup.sql
```

### Restore Database
```bash
docker-compose exec -T db mysql -u wordpress -pwordpress_password wordpress < backup.sql
```

## File Structure

```
.
├── Dockerfile              # WordPress container configuration
├── docker-compose.yml      # Multi-container setup
├── .dockerignore           # Files to exclude from Docker build
├── wp-content/             # WordPress themes, plugins, uploads
└── README-Docker.md        # This file
```

## Customization

### Environment Variables

You can customize the database settings by editing the environment variables in `docker-compose.yml`:

```yaml
environment:
  WORDPRESS_DB_HOST: db:3306
  WORDPRESS_DB_USER: your_username
  WORDPRESS_DB_PASSWORD: your_password
  WORDPRESS_DB_NAME: your_database
```

### Ports

Change the ports in `docker-compose.yml` if needed:

```yaml
ports:
  - "8080:80"  # Change 8080 to your preferred port
```

### PHP Configuration

The Dockerfile includes optimized PHP settings for WordPress. You can modify these in the Dockerfile:

```dockerfile
RUN { \
    echo 'memory_limit = 256M'; \
    echo 'upload_max_filesize = 64M'; \
    echo 'post_max_size = 64M'; \
} > /usr/local/etc/php/conf.d/wordpress.ini
```

## Troubleshooting

### Port Already in Use
If port 8080 is already in use, change it in docker-compose.yml:
```yaml
ports:
  - "8090:80"  # Use port 8090 instead
```

### Permission Issues
If you encounter permission issues:
```bash
sudo chown -R $USER:$USER wp-content/
```

### Database Connection Issues
Make sure the database container is running:
```bash
docker-compose logs db
```

## Production Notes

⚠️ **This setup is for development only**. For production:

1. Use environment variables for sensitive data
2. Set up proper SSL certificates
3. Configure proper backup strategies
4. Use production-ready database settings
5. Implement proper security measures
