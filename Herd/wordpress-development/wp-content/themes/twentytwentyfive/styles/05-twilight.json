{"$schema": "https://schemas.wp.org/wp/6.7/theme.json", "version": 3, "title": "Twilight", "settings": {"color": {"palette": [{"color": "#131313", "name": "Base", "slug": "base"}, {"color": "#FFFFFF", "name": "Contrast", "slug": "contrast"}, {"color": "#4B52FF", "name": "Accent 1", "slug": "accent-1"}, {"color": "#FF7A5C", "name": "Accent 2", "slug": "accent-2"}, {"color": "#252525", "name": "Accent 3", "slug": "accent-3"}, {"color": "#FFFFFF", "name": "Accent 4", "slug": "accent-4"}, {"color": "#252525", "name": "Accent 5", "slug": "accent-5"}, {"color": "#FFFFFF33", "name": "Accent 6", "slug": "accent-6"}]}, "typography": {"fontFamilies": [{"name": "<PERSON><PERSON> Slab", "slug": "roboto-slab", "fontFamily": "\"Roboto Slab\", serif", "fontFace": [{"fontFamily": "\"Roboto Slab\"", "fontStyle": "normal", "fontWeight": "100 900", "src": ["file:./assets/fonts/roboto-slab/RobotoSlab-VariableFont_wght.woff2"]}]}, {"name": "Manrope", "slug": "manrope", "fontFamily": "Man<PERSON><PERSON>, sans-serif", "fontFace": [{"src": ["file:./assets/fonts/manrope/Manrope-VariableFont_wght.woff2"], "fontWeight": "200 800", "fontStyle": "normal", "fontFamily": "Manrope"}]}], "fontSizes": [{"fluid": false, "name": "Small", "size": "0.875rem", "slug": "small"}, {"fluid": {"max": "1.125rem", "min": "1rem"}, "name": "Medium", "size": "1rem", "slug": "medium"}, {"fluid": {"max": "1.375rem", "min": "1.125rem"}, "name": "Large", "size": "1.38rem", "slug": "large"}, {"fluid": {"max": "2rem", "min": "1.75rem"}, "name": "Extra Large", "size": "1.75rem", "slug": "x-large"}, {"fluid": {"max": "2.4rem", "min": "2.15rem"}, "name": "Extra Extra Large", "size": "2.15rem", "slug": "xx-large"}]}}, "styles": {"typography": {"letterSpacing": "0"}, "blocks": {"core/button": {"variations": {"outline": {"spacing": {"padding": {"bottom": "0.625rem", "left": "1.375rem", "right": "1.375rem", "top": "0.625rem"}}}}}, "core/navigation": {"typography": {"fontSize": "var:preset|font-size|large", "letterSpacing": "-0.28px", "textTransform": "uppercase"}}, "core/post-author": {"typography": {"fontSize": "var:preset|font-size|small"}}, "core/post-author-name": {"typography": {"fontSize": "var:preset|font-size|small"}}, "core/post-terms": {"typography": {"fontWeight": "500"}}, "core/pullquote": {"typography": {"fontFamily": "var:preset|font-family|roboto-slab", "fontSize": "var:preset|font-size|xx-large", "fontWeight": "200"}}, "core/search": {"typography": {"textTransform": "uppercase"}}, "core/site-tagline": {"typography": {"fontSize": "var:preset|font-size|large"}}, "core/site-title": {"typography": {"textTransform": "uppercase"}}}, "elements": {"button": {"spacing": {"padding": {"bottom": "0.625rem", "left": "1.375rem", "right": "1.375rem", "top": "0.625rem"}}, "typography": {"fontWeight": "500", "letterSpacing": "-0.36px", "textTransform": "uppercase"}}, "heading": {"typography": {"fontFamily": "var:preset|font-family|roboto-slab", "fontWeight": "300", "letterSpacing": "-0.5px", "lineHeight": "1.2"}}}, "variations": {"section-2": {"color": {"text": "var:preset|color|base"}, "elements": {"button": {"color": {"background": "var:preset|color|base", "text": "var:preset|color|accent-2"}, ":hover": {"color": {"text": "var:preset|color|accent-2"}}}, "link": {"color": {"text": "currentColor"}}}}, "section-5": {"blocks": {"core/post-comments-form": {"css": "& textarea, input:not([type=submit]){border-radius:.25rem; border-color: color-mix(in srgb, currentColor 20%, transparent) !important;} & input[type=checkbox]{margin:0 .2rem 0 0 !important;} & label {font-size: var(--wp--preset--font-size--small); }"}, "core/search": {"css": "& .wp-block-search__input{border-color: color-mix(in srgb, currentColor 20%, transparent);}"}}}}}}