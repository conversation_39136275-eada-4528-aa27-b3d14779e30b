{"$schema": "https://schemas.wp.org/wp/6.5/theme.json", "version": 2, "title": "<PERSON><PERSON><PERSON>", "settings": {"color": {"palette": [{"color": "#38629F", "name": "Base", "slug": "base"}, {"color": "#244E8A", "name": "Base / Two", "slug": "base-2"}, {"color": "#FFFFFFA1", "name": "Contrast / 2", "slug": "contrast-2"}, {"color": "#FFFFFF", "name": "Contrast", "slug": "contrast"}, {"color": "#D5E0F0", "name": "Contrast / 3", "slug": "contrast-3"}]}, "typography": {"fontFamilies": [{"fontFace": [{"fontFamily": "<PERSON><PERSON>", "fontStyle": "normal", "fontWeight": "400", "src": ["file:./assets/fonts/cardo/cardo_normal_400.woff2"]}, {"fontFamily": "<PERSON><PERSON>", "fontStyle": "italic", "fontWeight": "400", "src": ["file:./assets/fonts/cardo/cardo_italic_400.woff2"]}, {"fontFamily": "<PERSON><PERSON>", "fontStyle": "normal", "fontWeight": "700", "src": ["file:./assets/fonts/cardo/cardo_normal_700.woff2"]}], "fontFamily": "<PERSON><PERSON>", "name": "<PERSON><PERSON>", "slug": "body"}, {"fontFace": [{"fontFamily": "<PERSON><PERSON>", "fontStyle": "normal", "fontWeight": "100 900", "src": ["file:./assets/fonts/jost/Jost-VariableFont_wght.woff2"]}, {"fontFamily": "<PERSON><PERSON>", "fontStyle": "italic", "fontWeight": "100 900", "src": ["file:./assets/fonts/jost/Jost-Italic-VariableFont_wght.woff2"]}], "fontFamily": "\"Jost\", sans-serif", "name": "<PERSON><PERSON>", "slug": "heading"}, {"fontFamily": "-apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif", "name": "System Sans-serif", "slug": "system-sans-serif"}, {"fontFamily": "Iowan Old Style, Apple Garamond, Baskerville, Times New Roman, Droid Serif, Times, Source Serif Pro, serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol", "name": "System Serif", "slug": "system-serif"}], "fontSizes": [{"fluid": false, "name": "Small", "size": "1rem", "slug": "small"}, {"fluid": false, "name": "Medium", "size": "1.2rem", "slug": "medium"}, {"fluid": {"min": "1.5rem", "max": "2rem"}, "name": "Large", "size": "2rem", "slug": "large"}, {"fluid": {"min": "2rem", "max": "2.65rem"}, "name": "Extra Large", "size": "2.65rem", "slug": "x-large"}, {"fluid": {"min": "2.65rem", "max": "3.5rem"}, "name": "Extra Extra Large", "size": "3.5rem", "slug": "xx-large"}]}}, "styles": {"blocks": {"core/button": {"variations": {"outline": {"spacing": {"padding": {"bottom": "calc(0.8rem - 2px)", "left": "calc(1.6rem - 2px)", "right": "calc(1.6rem - 2px)", "top": "calc(0.8rem - 2px)"}}, "border": {"width": "2px"}}}}, "core/site-title": {"typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontWeight": "normal"}}, "core/navigation": {"typography": {"fontSize": "var(--wp--preset--font-size--small)", "fontWeight": "normal"}}}, "elements": {"button": {"border": {"radius": "6px"}, "color": {"background": "var(--wp--preset--color--contrast)", "text": "var(--wp--preset--color--base-2)"}, "spacing": {"padding": {"bottom": "0.98rem", "left": "1.6rem", "right": "1.6rem", "top": "0.8rem"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "fontSize": "var(--wp--preset--font-size--small)", "fontStyle": "normal"}, ":hover": {"color": {"background": "var(--wp--preset--color--contrast)"}}}, "heading": {"typography": {"fontFamily": "var(--wp--preset--font-family--heading)", "letterSpacing": "0"}}}}}