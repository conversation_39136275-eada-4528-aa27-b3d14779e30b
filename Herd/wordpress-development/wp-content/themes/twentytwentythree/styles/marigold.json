{"$schema": "https://schemas.wp.org/wp/6.5/theme.json", "version": 2, "title": "Marigold", "settings": {"color": {"palette": [{"color": "#F6F2EC", "name": "Base", "slug": "base"}, {"color": "#21251F", "name": "Contrast", "slug": "contrast"}, {"color": "#5B4460", "name": "Primary", "slug": "primary"}, {"color": "#FCC263", "name": "Secondary", "slug": "secondary"}, {"color": "#E7A1A9", "name": "Tertiary", "slug": "tertiary"}]}, "layout": {"wideSize": "1200px"}, "spacing": {"spacingSizes": [{"size": "clamp(0.625rem, 0.434rem + 0.61vw, 0.938rem)", "name": "1", "slug": "30"}, {"size": "clamp(1.25rem, 0.869rem + 1.22vw, 1.875rem)", "name": "2", "slug": "40"}, {"size": "clamp(1.875rem, 1.303rem + 1.83vw, 2.813rem)", "name": "3", "slug": "50"}, {"size": "clamp(2.5rem, 1.738rem + 2.44vw, 3.75rem)", "name": "4", "slug": "60"}, {"size": "clamp(2.813rem, 1.098rem + 5.49vw, 5.625rem)", "name": "5", "slug": "70"}, {"size": "clamp(3.75rem, 1.463rem + 7.32vw, 7.5rem)", "name": "6", "slug": "80"}]}, "typography": {"fontSizes": [{"size": "clamp(0.875rem, 0.799rem + 0.24vw, 1rem)", "name": "Tiny", "slug": "tiny"}, {"size": "clamp(1rem, 0.924rem + 0.24vw, 1.125rem)", "slug": "small"}, {"size": "clamp(1.125rem, 1.049rem + 0.24vw, 1.25rem)", "name": "Normal", "slug": "normal"}, {"size": "clamp(1.25rem, 1.021rem + 0.73vw, 1.625rem)", "slug": "medium"}, {"size": "clamp(1.375rem, 1.07rem + 0.98vw, 1.875rem)", "slug": "large"}, {"size": "clamp(1.75rem, 1.369rem + 1.22vw, 2.375rem)", "slug": "x-large"}, {"size": "clamp(2.125rem, 1.706rem + 1.34vw, 2.813rem)", "slug": "xx-large"}, {"size": "clamp(2.5rem, 1.966rem + 1.71vw, 3.375rem)", "name": "<PERSON>ge", "slug": "huge"}, {"size": "clamp(3.375rem, 2.384rem + 3.17vw, 5rem)", "name": "Gigantic", "slug": "gigantic"}]}}, "styles": {"blocks": {"core/comment-author-name": {"elements": {"link": {":active": {"color": {"text": "var(--wp--preset--color--primary)"}}}}}, "core/query": {"spacing": {"padding": {"left": "0", "right": "0"}}}, "core/post-content": {"elements": {"link": {"color": {"text": "var(--wp--preset--color--primary)"}}}}, "core/post-excerpt": {"typography": {"fontSize": "var(--wp--preset--font-size--normal)"}}, "core/post-title": {"elements": {"link": {"typography": {"textDecoration": "none"}, "color": {"text": "var(--wp--preset--color--primary)"}}}, "spacing": {"margin": {"bottom": "var(--wp--preset--spacing--50)", "top": "var(--wp--preset--spacing--50)"}}, "typography": {"fontSize": "var(--wp--preset--font-size--large)", "fontWeight": "600"}}, "core/pullquote": {"border": {"width": "1px 0"}}, "core/query-pagination": {"elements": {"link": {"typography": {"textDecoration": "none"}}}, "typography": {"fontSize": "var(--wp--preset--font-size--small)", "fontWeight": "400"}}, "core/quote": {"elements": {"cite": {"typography": {"fontSize": "1.25rem"}}}, "typography": {"fontSize": "1.625rem", "lineHeight": "1.5"}}, "core/site-title": {"typography": {"fontSize": "var(--wp--preset--font-size--normal)", "textTransform": "lowercase"}}}, "elements": {"h1": {"typography": {"fontSize": "var(--wp--preset--font-size--huge)", "lineHeight": "1.1"}}, "h2": {"typography": {"fontSize": "var(--wp--preset--font-size--xx-large)", "lineHeight": "1.2"}}, "h3": {"typography": {"fontSize": "var(--wp--preset--font-size--x-large)", "lineHeight": "1.2"}}, "h4": {"typography": {"fontSize": "var(--wp--preset--font-size--large)", "fontWeight": "600"}}, "h5": {"typography": {"fontStyle": "normal", "fontWeight": "600", "textTransform": "none"}}, "h6": {"typography": {"fontSize": "var(--wp--preset--font-size--normal)", "fontStyle": "normal", "fontWeight": "600"}}, "heading": {"typography": {"fontStyle": "italic"}}, "link": {":active": {"color": {"text": "var(--wp--preset--color--primary)"}}, ":hover": {"typography": {"textDecoration": "none"}}}, "button": {"border": {"radius": "50px"}, "color": {"background": "var(--wp--preset--color--secondary)"}, "typography": {"fontSize": "var(--wp--preset--font-size--normal)"}, ":hover": {"color": {"background": "var(--wp--preset--color--tertiary)", "text": "var(--wp--preset--color--contrast)"}}, ":focus": {"color": {"background": "var(--wp--preset--color--primary)"}}, ":active": {"color": {"background": "var(--wp--preset--color--primary)"}}}}, "spacing": {"blockGap": "2.5rem", "padding": {"bottom": "var(--wp--preset--spacing--50)", "left": "var(--wp--preset--spacing--40)", "right": "var(--wp--preset--spacing--40)", "top": "var(--wp--preset--spacing--50)"}}, "typography": {"fontFamily": "var(--wp--preset--font-family--source-serif-pro)", "fontSize": "var(--wp--preset--font-size--normal)", "lineHeight": "1.5"}}}