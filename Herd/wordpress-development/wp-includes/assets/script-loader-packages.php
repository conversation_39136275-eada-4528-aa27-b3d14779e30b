<?php return array('a11y.js' => array('dependencies' => array('wp-dom-ready', 'wp-i18n'), 'version' => '604a4359a838a9073d9f'), 'annotations.js' => array('dependencies' => array('wp-data', 'wp-hooks', 'wp-i18n', 'wp-rich-text'), 'version' => 'dfaab3186d5cfee4a4c2'), 'api-fetch.js' => array('dependencies' => array('wp-i18n', 'wp-url'), 'version' => '52446bd41c30bc419a05'), 'autop.js' => array('dependencies' => array(), 'version' => 'a0567e518e6863383ace'), 'blob.js' => array('dependencies' => array('wp-polyfill'), 'version' => '65d38acd0a443932b695'), 'block-directory.js' => array('dependencies' => array('react-jsx-runtime', 'wp-a11y', 'wp-api-fetch', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-editor', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-notices', 'wp-plugins', 'wp-primitives', 'wp-url'), 'version' => '4663e7373a239c52d743'), 'block-editor.js' => array('dependencies' => array('react', 'react-dom', 'react-jsx-runtime', 'wp-a11y', 'wp-api-fetch', 'wp-blob', 'wp-block-serialization-default-parser', 'wp-blocks', 'wp-commands', 'wp-components', 'wp-compose', 'wp-data', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-is-shallow-equal', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-notices', 'wp-polyfill', 'wp-preferences', 'wp-primitives', 'wp-priority-queue', 'wp-private-apis', 'wp-rich-text', 'wp-style-engine', 'wp-token-list', 'wp-url', 'wp-warning'), 'version' => 'd5b81eb0e8bc8d678b05'), 'block-library.js' => array('dependencies' => array('react-jsx-runtime', 'wp-a11y', 'wp-api-fetch', 'wp-autop', 'wp-blob', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-escape-html', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-notices', 'wp-patterns', 'wp-polyfill', 'wp-primitives', 'wp-private-apis', 'wp-rich-text', 'wp-server-side-render', 'wp-url', 'wp-wordcount'), 'version' => '4973f92abc0198544fdb'), 'block-serialization-default-parser.js' => array('dependencies' => array(), 'version' => '1d1bef54e84a98f3efb9'), 'blocks.js' => array('dependencies' => array('react-jsx-runtime', 'wp-autop', 'wp-blob', 'wp-block-serialization-default-parser', 'wp-data', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-is-shallow-equal', 'wp-polyfill', 'wp-private-apis', 'wp-rich-text', 'wp-shortcode', 'wp-warning'), 'version' => 'e851ee8e9644a9abf7ed'), 'commands.js' => array('dependencies' => array('react', 'react-dom', 'react-jsx-runtime', 'wp-components', 'wp-data', 'wp-element', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-primitives', 'wp-private-apis'), 'version' => 'afc09c8bf245fe46c7a0'), 'components.js' => array('dependencies' => array('react', 'react-dom', 'react-jsx-runtime', 'wp-a11y', 'wp-compose', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-escape-html', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-is-shallow-equal', 'wp-keycodes', 'wp-primitives', 'wp-private-apis', 'wp-rich-text', 'wp-warning'), 'version' => '0526106ae8fcc5b2e62d'), 'compose.js' => array('dependencies' => array('react', 'react-jsx-runtime', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-is-shallow-equal', 'wp-keycodes', 'wp-priority-queue'), 'version' => 'ccc1a46c6a7b3734d300'), 'core-commands.js' => array('dependencies' => array('react-jsx-runtime', 'wp-commands', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-notices', 'wp-primitives', 'wp-private-apis', 'wp-router', 'wp-url'), 'version' => 'a0115e38272e1800dbd2'), 'core-data.js' => array('dependencies' => array('react-jsx-runtime', 'wp-api-fetch', 'wp-block-editor', 'wp-blocks', 'wp-compose', 'wp-data', 'wp-deprecated', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-is-shallow-equal', 'wp-private-apis', 'wp-rich-text', 'wp-url', 'wp-warning'), 'version' => '70ddde93f4f568925186'), 'customize-widgets.js' => array('dependencies' => array('react-jsx-runtime', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-is-shallow-equal', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-preferences', 'wp-primitives', 'wp-private-apis', 'wp-widgets'), 'version' => '96ce1edc9b683b77847c'), 'data.js' => array('dependencies' => array('react-jsx-runtime', 'wp-compose', 'wp-deprecated', 'wp-element', 'wp-is-shallow-equal', 'wp-priority-queue', 'wp-private-apis', 'wp-redux-routine'), 'version' => '2797f012cdb7d180a746'), 'data-controls.js' => array('dependencies' => array('wp-api-fetch', 'wp-data', 'wp-deprecated'), 'version' => 'e31cdcc73f3eea4fbe01'), 'date.js' => array('dependencies' => array('moment', 'wp-deprecated'), 'version' => '93f88d98de5601d8a46d'), 'deprecated.js' => array('dependencies' => array('wp-hooks'), 'version' => '741e32edb0e7c2dd30da'), 'dom.js' => array('dependencies' => array('wp-deprecated'), 'version' => '5c5821604f66f88e084b'), 'dom-ready.js' => array('dependencies' => array(), 'version' => '5b9fa8df0892dc9a7c41'), 'edit-post.js' => array('dependencies' => array('react-jsx-runtime', 'wp-api-fetch', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-commands', 'wp-components', 'wp-compose', 'wp-core-commands', 'wp-core-data', 'wp-data', 'wp-deprecated', 'wp-editor', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-notices', 'wp-plugins', 'wp-preferences', 'wp-primitives', 'wp-private-apis', 'wp-url', 'wp-widgets'), 'version' => 'dc6bbea5a448261ec8de'), 'edit-site.js' => array('dependencies' => array('react', 'react-dom', 'react-jsx-runtime', 'wp-a11y', 'wp-api-fetch', 'wp-blob', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-commands', 'wp-components', 'wp-compose', 'wp-core-commands', 'wp-core-data', 'wp-data', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-editor', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-notices', 'wp-patterns', 'wp-plugins', 'wp-polyfill', 'wp-preferences', 'wp-primitives', 'wp-private-apis', 'wp-router', 'wp-url', 'wp-warning', 'wp-widgets'), 'version' => '2dd42f9ef237a6be1b5e'), 'edit-widgets.js' => array('dependencies' => array('react-jsx-runtime', 'wp-api-fetch', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-notices', 'wp-patterns', 'wp-plugins', 'wp-preferences', 'wp-primitives', 'wp-private-apis', 'wp-url', 'wp-viewport', 'wp-widgets'), 'version' => '47b223cb88d85c713a6b'), 'editor.js' => array('dependencies' => array('react', 'react-jsx-runtime', 'wp-a11y', 'wp-api-fetch', 'wp-blob', 'wp-block-editor', 'wp-blocks', 'wp-commands', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-notices', 'wp-patterns', 'wp-plugins', 'wp-polyfill', 'wp-preferences', 'wp-primitives', 'wp-private-apis', 'wp-rich-text', 'wp-server-side-render', 'wp-url', 'wp-viewport', 'wp-warning', 'wp-wordcount'), 'version' => '4faaac0109b050f1dbb2'), 'element.js' => array('dependencies' => array('react', 'react-dom', 'wp-escape-html'), 'version' => '6bd445740b34f5eae604'), 'escape-html.js' => array('dependencies' => array(), 'version' => '93558693d672af42c190'), 'format-library.js' => array('dependencies' => array('react-jsx-runtime', 'wp-a11y', 'wp-block-editor', 'wp-components', 'wp-data', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-primitives', 'wp-private-apis', 'wp-rich-text', 'wp-url'), 'version' => '36151f5b565e0cdf6ddc'), 'hooks.js' => array('dependencies' => array(), 'version' => 'be67dc331e61e06d52fa'), 'html-entities.js' => array('dependencies' => array(), 'version' => '0d1913e5b8fb9137bad2'), 'i18n.js' => array('dependencies' => array('wp-hooks'), 'version' => '5edc734adb78e0d7d00e'), 'is-shallow-equal.js' => array('dependencies' => array(), 'version' => '58ed73f7376c883f832b'), 'keyboard-shortcuts.js' => array('dependencies' => array('react-jsx-runtime', 'wp-data', 'wp-element', 'wp-keycodes'), 'version' => 'c0e19c4aa8550cb4f71d'), 'keycodes.js' => array('dependencies' => array('wp-i18n'), 'version' => '2bad5660ad4ebde6540c'), 'list-reusable-blocks.js' => array('dependencies' => array('react-jsx-runtime', 'wp-api-fetch', 'wp-blob', 'wp-components', 'wp-compose', 'wp-element', 'wp-i18n'), 'version' => 'ad48bd203f06c82d4c77'), 'media-utils.js' => array('dependencies' => array('wp-api-fetch', 'wp-blob', 'wp-element', 'wp-i18n', 'wp-private-apis'), 'version' => '6c862a9b6eee311930ac'), 'notices.js' => array('dependencies' => array('wp-data'), 'version' => 'bb7ea4346f0c7a77df98'), 'nux.js' => array('dependencies' => array('react-jsx-runtime', 'wp-components', 'wp-compose', 'wp-data', 'wp-deprecated', 'wp-element', 'wp-i18n', 'wp-primitives'), 'version' => 'a8290a113a755fbd665b'), 'patterns.js' => array('dependencies' => array('react-jsx-runtime', 'wp-a11y', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-element', 'wp-html-entities', 'wp-i18n', 'wp-notices', 'wp-primitives', 'wp-private-apis', 'wp-url'), 'version' => 'ea1ca8506283289a8aeb'), 'plugins.js' => array('dependencies' => array('react-jsx-runtime', 'wp-compose', 'wp-deprecated', 'wp-element', 'wp-hooks', 'wp-is-shallow-equal', 'wp-primitives'), 'version' => '7313a68349c296697e15'), 'preferences.js' => array('dependencies' => array('react-jsx-runtime', 'wp-a11y', 'wp-components', 'wp-compose', 'wp-data', 'wp-deprecated', 'wp-element', 'wp-i18n', 'wp-primitives', 'wp-private-apis'), 'version' => 'f49ac7669ac11f416b6f'), 'preferences-persistence.js' => array('dependencies' => array('wp-api-fetch'), 'version' => 'a5baddbc610561581693'), 'primitives.js' => array('dependencies' => array('react-jsx-runtime', 'wp-element'), 'version' => '66632613c3c6b0ed6f76'), 'priority-queue.js' => array('dependencies' => array(), 'version' => 'be4e4334602693fa7256'), 'private-apis.js' => array('dependencies' => array(), 'version' => '18ea1d568a3bfd485afb'), 'redux-routine.js' => array('dependencies' => array(), 'version' => '9473249104d09cb1245d'), 'reusable-blocks.js' => array('dependencies' => array('react-jsx-runtime', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-core-data', 'wp-data', 'wp-element', 'wp-i18n', 'wp-notices', 'wp-primitives', 'wp-url'), 'version' => '03eefa7ca729412606a7'), 'rich-text.js' => array('dependencies' => array('wp-a11y', 'wp-compose', 'wp-data', 'wp-deprecated', 'wp-element', 'wp-escape-html', 'wp-i18n', 'wp-keycodes'), 'version' => 'bc76e6f025d3556aa54a'), 'router.js' => array('dependencies' => array('react-jsx-runtime', 'wp-compose', 'wp-element', 'wp-polyfill', 'wp-private-apis', 'wp-url'), 'version' => '99ea3f320332583c3e5c'), 'server-side-render.js' => array('dependencies' => array('react-jsx-runtime', 'wp-api-fetch', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-data', 'wp-element', 'wp-i18n', 'wp-url'), 'version' => '5058284dc05b1b1b2661'), 'shortcode.js' => array('dependencies' => array(), 'version' => '37060a10de8dd991d95d'), 'style-engine.js' => array('dependencies' => array(), 'version' => '95bc9b8c9f4f0e8a6423'), 'token-list.js' => array('dependencies' => array(), 'version' => '09fdc83606f766278b8b'), 'url.js' => array('dependencies' => array('wp-polyfill'), 'version' => 'de0b7a9192bc77a4d766'), 'viewport.js' => array('dependencies' => array('react-jsx-runtime', 'wp-compose', 'wp-data'), 'version' => '82036eb97185ee78762b'), 'warning.js' => array('dependencies' => array(), 'version' => '4ecd4ff4d8fa94314090'), 'widgets.js' => array('dependencies' => array('react-jsx-runtime', 'wp-api-fetch', 'wp-block-editor', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-element', 'wp-i18n', 'wp-notices', 'wp-polyfill', 'wp-primitives'), 'version' => 'b2d3d6f0ca283bd27cbc'), 'wordcount.js' => array('dependencies' => array(), 'version' => 'f5354b03b95c624877fb'));
