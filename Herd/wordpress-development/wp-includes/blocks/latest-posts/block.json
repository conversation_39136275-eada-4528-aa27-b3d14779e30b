{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/latest-posts", "title": "Latest Posts", "category": "widgets", "description": "Display a list of your most recent posts.", "keywords": ["recent posts"], "textdomain": "default", "attributes": {"categories": {"type": "array", "items": {"type": "object"}}, "selectedAuthor": {"type": "number"}, "postsToShow": {"type": "number", "default": 5}, "displayPostContent": {"type": "boolean", "default": false}, "displayPostContentRadio": {"type": "string", "default": "excerpt"}, "excerptLength": {"type": "number", "default": 55}, "displayAuthor": {"type": "boolean", "default": false}, "displayPostDate": {"type": "boolean", "default": false}, "postLayout": {"type": "string", "default": "list"}, "columns": {"type": "number", "default": 3}, "order": {"type": "string", "default": "desc"}, "orderBy": {"type": "string", "default": "date"}, "displayFeaturedImage": {"type": "boolean", "default": false}, "featuredImageAlign": {"type": "string", "enum": ["left", "center", "right"]}, "featuredImageSizeSlug": {"type": "string", "default": "thumbnail"}, "featuredImageSizeWidth": {"type": "number", "default": null}, "featuredImageSizeHeight": {"type": "number", "default": null}, "addLinkToFeaturedImage": {"type": "boolean", "default": false}}, "supports": {"align": true, "html": false, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true, "link": true}}, "spacing": {"margin": true, "padding": true}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "__experimentalBorder": {"radius": true, "color": true, "width": true, "style": true, "__experimentalDefaultControls": {"radius": true, "color": true, "width": true, "style": true}}, "interactivity": {"clientNavigation": true}}, "editorStyle": "wp-block-latest-posts-editor", "style": "wp-block-latest-posts"}