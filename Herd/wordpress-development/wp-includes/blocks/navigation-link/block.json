{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/navigation-link", "title": "Custom Link", "category": "design", "parent": ["core/navigation"], "allowedBlocks": ["core/navigation-link", "core/navigation-submenu", "core/page-list"], "description": "Add a page, link, or another item to your navigation.", "textdomain": "default", "attributes": {"label": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}, "rel": {"type": "string"}, "id": {"type": "number"}, "opensInNewTab": {"type": "boolean", "default": false}, "url": {"type": "string"}, "title": {"type": "string"}, "kind": {"type": "string"}, "isTopLevelLink": {"type": "boolean"}}, "usesContext": ["textColor", "customTextColor", "backgroundColor", "customBackgroundColor", "overlayTextColor", "customOverlayTextColor", "overlayBackgroundColor", "customOverlayBackgroundColor", "fontSize", "customFontSize", "showSubmenuIcon", "maxNestingLevel", "style"], "supports": {"reusable": false, "html": false, "__experimentalSlashInserter": true, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "renaming": false, "interactivity": {"clientNavigation": true}}, "editorStyle": "wp-block-navigation-link-editor", "style": "wp-block-navigation-link"}