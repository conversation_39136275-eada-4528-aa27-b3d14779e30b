{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/post-author-biography", "title": "Author Biography", "category": "theme", "description": "The author biography.", "textdomain": "default", "attributes": {"textAlign": {"type": "string"}}, "usesContext": ["postType", "postId"], "example": {"viewportWidth": 350}, "supports": {"spacing": {"margin": true, "padding": true}, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "interactivity": {"clientNavigation": true}, "__experimentalBorder": {"radius": true, "color": true, "width": true, "style": true, "__experimentalDefaultControls": {"radius": true, "color": true, "width": true, "style": true}}}, "style": "wp-block-post-author-biography"}