.wp-block-post-template{
  box-sizing:border-box;
  list-style:none;
  margin-bottom:0;
  margin-top:0;
  max-width:100%;
  padding:0;
}
.wp-block-post-template.is-flex-container{
  display:flex;
  flex-direction:row;
  flex-wrap:wrap;
  gap:1.25em;
}
.wp-block-post-template.is-flex-container>li{
  margin:0;
  width:100%;
}
@media (min-width:600px){
  .wp-block-post-template.is-flex-container.is-flex-container.columns-2>li{
    width:calc(50% - .625em);
  }
  .wp-block-post-template.is-flex-container.is-flex-container.columns-3>li{
    width:calc(33.33333% - .83333em);
  }
  .wp-block-post-template.is-flex-container.is-flex-container.columns-4>li{
    width:calc(25% - .9375em);
  }
  .wp-block-post-template.is-flex-container.is-flex-container.columns-5>li{
    width:calc(20% - 1em);
  }
  .wp-block-post-template.is-flex-container.is-flex-container.columns-6>li{
    width:calc(16.66667% - 1.04167em);
  }
}

@media (max-width:600px){
  .wp-block-post-template-is-layout-grid.wp-block-post-template-is-layout-grid.wp-block-post-template-is-layout-grid.wp-block-post-template-is-layout-grid{
    grid-template-columns:1fr;
  }
}
.wp-block-post-template-is-layout-constrained>li>.alignright,.wp-block-post-template-is-layout-flow>li>.alignright{
  float:right;
  margin-inline-end:0;
  margin-inline-start:2em;
}

.wp-block-post-template-is-layout-constrained>li>.alignleft,.wp-block-post-template-is-layout-flow>li>.alignleft{
  float:left;
  margin-inline-end:2em;
  margin-inline-start:0;
}

.wp-block-post-template-is-layout-constrained>li>.aligncenter,.wp-block-post-template-is-layout-flow>li>.aligncenter{
  margin-inline-end:auto;
  margin-inline-start:auto;
}