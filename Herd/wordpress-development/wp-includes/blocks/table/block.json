{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/table", "title": "Table", "category": "text", "description": "Create structured content in rows and columns to display information.", "textdomain": "default", "attributes": {"hasFixedLayout": {"type": "boolean", "default": true}, "caption": {"type": "rich-text", "source": "rich-text", "selector": "figcaption"}, "head": {"type": "array", "default": [], "source": "query", "selector": "thead tr", "query": {"cells": {"type": "array", "default": [], "source": "query", "selector": "td,th", "query": {"content": {"type": "rich-text", "source": "rich-text"}, "tag": {"type": "string", "default": "td", "source": "tag"}, "scope": {"type": "string", "source": "attribute", "attribute": "scope"}, "align": {"type": "string", "source": "attribute", "attribute": "data-align"}, "colspan": {"type": "string", "source": "attribute", "attribute": "colspan"}, "rowspan": {"type": "string", "source": "attribute", "attribute": "rowspan"}}}}}, "body": {"type": "array", "default": [], "source": "query", "selector": "tbody tr", "query": {"cells": {"type": "array", "default": [], "source": "query", "selector": "td,th", "query": {"content": {"type": "rich-text", "source": "rich-text"}, "tag": {"type": "string", "default": "td", "source": "tag"}, "scope": {"type": "string", "source": "attribute", "attribute": "scope"}, "align": {"type": "string", "source": "attribute", "attribute": "data-align"}, "colspan": {"type": "string", "source": "attribute", "attribute": "colspan"}, "rowspan": {"type": "string", "source": "attribute", "attribute": "rowspan"}}}}}, "foot": {"type": "array", "default": [], "source": "query", "selector": "tfoot tr", "query": {"cells": {"type": "array", "default": [], "source": "query", "selector": "td,th", "query": {"content": {"type": "rich-text", "source": "rich-text"}, "tag": {"type": "string", "default": "td", "source": "tag"}, "scope": {"type": "string", "source": "attribute", "attribute": "scope"}, "align": {"type": "string", "source": "attribute", "attribute": "data-align"}, "colspan": {"type": "string", "source": "attribute", "attribute": "colspan"}, "rowspan": {"type": "string", "source": "attribute", "attribute": "rowspan"}}}}}}, "supports": {"anchor": true, "align": true, "color": {"__experimentalSkipSerialization": true, "gradients": true, "__experimentalDefaultControls": {"background": true, "text": true}}, "spacing": {"margin": true, "padding": true, "__experimentalDefaultControls": {"margin": false, "padding": false}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontStyle": true, "__experimentalFontWeight": true, "__experimentalLetterSpacing": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalDefaultControls": {"fontSize": true}}, "__experimentalBorder": {"__experimentalSkipSerialization": true, "color": true, "style": true, "width": true, "__experimentalDefaultControls": {"color": true, "style": true, "width": true}}, "interactivity": {"clientNavigation": true}}, "selectors": {"root": ".wp-block-table > table", "spacing": ".wp-block-table"}, "styles": [{"name": "regular", "label": "<PERSON><PERSON><PERSON>", "isDefault": true}, {"name": "stripes", "label": "Stripes"}], "editorStyle": "wp-block-table-editor", "style": "wp-block-table"}